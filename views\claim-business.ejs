<%- include('layout', { body: `
    <!-- Claim <PERSON> -->
    <section class="bg-blue-600 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h1 class="text-4xl font-bold mb-4">Claim <%= business.name %></h1>
            <p class="text-xl text-blue-100">Verify your ownership of this business</p>
        </div>
    </section>

    <!-- Claim Form -->
    <section class="py-16">
        <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="bg-white rounded-lg shadow-md p-8">
                <h2 class="text-2xl font-bold mb-6">Business Claim Form</h2>
                
                <div class="mb-6 p-4 bg-blue-50 rounded-lg">
                    <h3 class="font-semibold mb-2">Business Details</h3>
                    <p><strong>Name:</strong> <%= business.name %></p>
                    <p><strong>Address:</strong> <%= business.address %>, <%= business.city_name %>, <%= business.state_code %></p>
                    <p><strong>Phone:</strong> <%= business.phone %></p>
                </div>

                <form id="claim-form" enctype="multipart/form-data">
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Tell us about your business
                        </label>
                        <textarea 
                            name="message" 
                            rows="4" 
                            class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="Please provide details about your business, your role, and any relevant information to verify your ownership..."
                            required
                        ></textarea>
                    </div>

                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Upload Documents (Optional)
                        </label>
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                            <div class="space-y-1 text-center">
                                <i class="fas fa-cloud-upload-alt text-3xl text-gray-400"></i>
                                <div class="flex text-sm text-gray-600">
                                    <label for="documents" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500">
                                        <span>Upload files</span>
                                        <input id="documents" name="documents" type="file" class="sr-only" multiple accept=".pdf,.jpg,.jpeg,.png">
                                    </label>
                                    <p class="pl-1">or drag and drop</p>
                                </div>
                                <p class="text-xs text-gray-500">
                                    PDF, JPG, PNG up to 5MB each
                                </p>
                            </div>
                        </div>
                        <div id="file-list" class="mt-2 text-sm text-gray-600"></div>
                    </div>

                    <div class="mb-6">
                        <label class="flex items-center">
                            <input type="checkbox" class="rounded border-gray-300" required>
                            <span class="ml-2 text-sm text-gray-700">
                                I confirm that I am the rightful owner or authorized representative of this business
                            </span>
                        </label>
                    </div>

                    <div class="flex space-x-4">
                        <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition">
                            Submit Claim
                        </button>
                        <a href="/businesses/<%= business.slug %>" class="bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 transition">
                            Cancel
                        </a>
                    </div>
                </form>

                <div id="response-message" class="mt-4"></div>
            </div>
        </div>
    </section>

    <!-- Claim Process -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-2xl font-bold mb-6 text-center">How It Works</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-file-alt text-blue-600 text-2xl"></i>
                    </div>
                    <h3 class="font-semibold mb-2">Submit Claim</h3>
                    <p class="text-gray-600 text-sm">Fill out the form with your business details and supporting documents</p>
                </div>
                
                <div class="text-center">
                    <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-clock text-blue-600 text-2xl"></i>
                    </div>
                    <h3 class="font-semibold mb-2">Review Process</h3>
                    <p class="text-gray-600 text-sm">Our team will review your claim within 2-3 business days</p>
                </div>
                
                <div class="text-center">
                    <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-check-circle text-blue-600 text-2xl"></i>
                    </div>
                    <h3 class="font-semibold mb-2">Get Approved</h3>
                    <p class="text-gray-600 text-sm">Once approved, you'll have full control over your business listing</p>
                </div>
            </div>
        </div>
    </section>
`, script: `
    <script>
        // File upload handling
        const fileInput = document.getElementById('documents');
        const fileList = document.getElementById('file-list');
        
        fileInput.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            fileList.innerHTML = '';
            
            files.forEach(file => {
                const div = document.createElement('div');
                div.className = 'text-sm text-gray-600';
                div.innerHTML = '<i class="fas fa-file mr-2"></i>' + file.name;
                fileList.appendChild(div);
            });
        });

        // Form submission
        document.getElementById('claim-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData();
            formData.append('message', document.querySelector('textarea[name="message"]').value);
            
            const files = document.getElementById('documents').files;
            for (let i = 0; i < files.length; i++) {
                formData.append('documents', files[i]);
            }

            try {
                const response = await fetch('/api/businesses/<%= business.id %>/claim', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'Authorization': 'Bearer ' + localStorage.getItem('token')
                    }
                });

                const result = await response.json();
                
                if (response.ok) {
                    document.getElementById('response-message').innerHTML = 
                        '<div class="bg-green-100 text-green-800 p-4 rounded-lg">Claim submitted successfully! We will review your request within 2-3 business days.</div>';
                    document.getElementById('claim-form').reset();
                } else {
                    document.getElementById('response-message').innerHTML = 
                        '<div class="bg-red-100 text-red-800 p-4 rounded-lg">Error: ' + result.error + '</div>';
                }
            } catch (error) {
                document.getElementById('response-message').innerHTML = 
                    '<div class="bg-red-100 text-red-800 p-4 rounded-lg">Error submitting claim. Please try again.</div>';
            }
        });
    </script>
`, title: \`Claim \${business.name}\`, script: \`
    <script>
        // Claim form submission
        document.getElementById('claim-form')?.addEventListener('submit', async function(e) {
            e.preventDefault();

            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Submitting...';
            submitBtn.disabled = true;

            try {
                const formData = new FormData(this);
                const response = await fetch('/api/businesses/<%= business.id %>/claim', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (response.ok) {
                    document.getElementById('response-message').innerHTML =
                        '<div class="bg-green-100 text-green-800 p-4 rounded-lg">Claim submitted successfully! We will review your request within 2-3 business days.</div>';
                    document.getElementById('claim-form').reset();
                } else {
                    document.getElementById('response-message').innerHTML =
                        '<div class="bg-red-100 text-red-800 p-4 rounded-lg">Error: ' + result.error + '</div>';
                }
            } catch (error) {
                document.getElementById('response-message').innerHTML =
                    '<div class="bg-red-100 text-red-800 p-4 rounded-lg">Error submitting claim. Please try again.</div>';
            }
        });
    </script>
\` }) %>
