<%- include('layout', { body: `
    <!-- State Header -->
    <section class="bg-blue-600 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h1 class="text-4xl font-bold mb-4">Pressure Washing Services in <%= state.name %></h1>
            <p class="text-xl text-blue-100"><%= state.business_count %> businesses found</p>
        </div>
    </section>

    <!-- Cities Section -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-2xl font-bold mb-6">Cities in <%= state.name %></h2>
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <% cities.forEach(city => { %>
                    <a href="/states/<%= state.slug %>/<%= city.slug %>" 
                       class="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition duration-300">
                        <h3 class="font-semibold text-gray-900"><%= city.name %></h3>
                        <p class="text-sm text-gray-600"><%= city.business_count %> businesses</p>
                    </a>
                <% }) %>
            </div>
        </div>
    </section>

    <!-- Businesses Section -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-2xl font-bold mb-6">All Businesses in <%= state.name %></h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <% businesses.forEach(business => { %>
                    <div class="bg-white rounded-lg shadow-md overflow-hidden">
                        <div class="p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                <a href="/businesses/<%= business.slug %>" class="hover:text-blue-600">
                                    <%= business.name %>
                                </a>
                            </h3>
                            <p class="text-gray-600 text-sm mb-3"><%= business.description %></p>
                            
                            <div class="flex items-center mb-3">
                                <div class="flex text-yellow-400">
                                    <% for (let i = 1; i <= 5; i++) { %>
                                        <i class="fas fa-star <%= i <= business.rating ? 'text-yellow-400' : 'text-gray-300' %>"></i>
                                    <% } %>
                                </div>
                                <span class="ml-2 text-sm text-gray-600"><%= business.rating %> (<%= business.review_count %>)</span>
                                <% if (business.is_verified) { %>
                                    <span class="ml-2 bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                        <i class="fas fa-check-circle mr-1"></i>Verified
                                    </span>
                                <% } %>
                            </div>
                            
                            <div class="text-sm text-gray-600">
                                <div class="flex items-center mb-1">
                                    <i class="fas fa-map-marker-alt mr-2"></i>
                                    <%= business.city_name %>, <%= state.code %>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-phone mr-2"></i>
                                    <%= business.phone %>
                                </div>
                            </div>
                        </div>
                    </div>
                <% }) %>
            </div>
        </div>
    </section>
`, title: `Pressure Washing Services in ${state.name}`, script: '' }) %>
