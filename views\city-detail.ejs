<%- include('layout', { body: `
    <!-- City Header -->
    <section class="bg-blue-600 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h1 class="text-4xl font-bold mb-4">Pressure Washing Services in <%= city.name %>, <%= city.state_code %></h1>
            <p class="text-xl text-blue-100"><%= city.business_count %> businesses found</p>
        </div>
    </section>

    <!-- Back Navigation -->
    <section class="py-8 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <a href="/states/<%= city.state_slug %>" class="text-blue-600 hover:text-blue-800">
                <i class="fas fa-arrow-left mr-2"></i>Back to <%= city.state_name %>
            </a>
        </div>
    </section>

    <!-- Businesses Section -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <% businesses.forEach(business => { %>
                    <div class="bg-white rounded-lg shadow-md overflow-hidden">
                        <div class="p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                <a href="/businesses/<%= business.slug %>" class="hover:text-blue-600">
                                    <%= business.name %>
                                </a>
                            </h3>
                            <p class="text-gray-600 text-sm mb-3"><%= business.description %></p>
                            
                            <div class="flex items-center mb-3">
                                <div class="flex text-yellow-400">
                                    <% for (let i = 1; i <= 5; i++) { %>
                                        <i class="fas fa-star <%= i <= business.rating ? 'text-yellow-400' : 'text-gray-300' %>"></i>
                                    <% } %>
                                </div>
                                <span class="ml-2 text-sm text-gray-600"><%= business.rating %> (<%= business.review_count %>)</span>
                                <% if (business.is_verified) { %>
                                    <span class="ml-2 bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                        <i class="fas fa-check-circle mr-1"></i>Verified
                                    </span>
                                <% } %>
                            </div>
                            
                            <div class="text-sm text-gray-600">
                                <div class="flex items-center mb-1">
                                    <i class="fas fa-map-marker-alt mr-2"></i>
                                    <%= business.address %>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-phone mr-2"></i>
                                    <%= business.phone %>
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                                    <%= business.category_name %>
                                </span>
                            </div>
                        </div>
                    </div>
                <% }) %>
            </div>
            
            <% if (businesses.length === 0) { %>
                <div class="text-center py-12">
                    <i class="fas fa-search text-6xl text-gray-300 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">No businesses found</h3>
                    <p class="text-gray-600">There are no pressure washing services listed in <%= city.name %> yet.</p>
                </div>
            <% } %>
        </div>
    </section>
`, title: `Pressure Washing Services in ${city.name}, ${city.state_code}`, script: '' }) %>
