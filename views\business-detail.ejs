<%- include('layout', { body: `
    <!-- Business Header -->
    <section class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="flex flex-col md:flex-row justify-between items-start">
                <div>
                    <h1 class="text-3xl font-bold mb-2"><%= business.name %></h1>
                    <p class="text-gray-600 mb-4"><%= business.description %></p>
                    
                    <div class="flex items-center mb-4">
                        <div class="flex text-yellow-400">
                            <% for (let i = 1; i <= 5; i++) { %>
                                <i class="fas fa-star <%= i <= business.rating ? 'text-yellow-400' : 'text-gray-300' %>"></i>
                            <% } %>
                        </div>
                        <span class="ml-2 text-gray-600"><%= business.rating %> (<%= business.review_count %> reviews)</span>
                        <% if (business.is_verified) { %>
                            <span class="ml-4 bg-green-100 text-green-800 text-sm px-3 py-1 rounded-full">
                                <i class="fas fa-check-circle mr-1"></i>Verified Business
                            </span>
                        <% } %>
                    </div>
                    
                    <div class="text-gray-600">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            <%= business.address %>, <%= business.city_name %>, <%= business.state_code %> <%= business.postal_code %>
                        </div>
                        <div class="flex items-center mb-2">
                            <i class="fas fa-phone mr-2"></i>
                            <%= business.phone %>
                        </div>
                        <% if (business.email) { %>
                            <div class="flex items-center mb-2">
                                <i class="fas fa-envelope mr-2"></i>
                                <%= business.email %>
                            </div>
                        <% } %>
                        <% if (business.website) { %>
                            <div class="flex items-center">
                                <i class="fas fa-globe mr-2"></i>
                                <a href="<%= business.website %>" target="_blank" class="text-blue-600 hover:underline">
                                    <%= business.website %>
                                </a>
                            </div>
                        <% } %>
                    </div>
                </div>
                
                <div class="mt-6 md:mt-0">
                    <% if (!business.is_claimed) { %>
                        <a href="/businesses/<%= business.slug %>/claim" 
                           class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition">
                            <i class="fas fa-handshake mr-2"></i>Claim This Business
                        </a>
                    <% } else { %>
                        <span class="bg-green-100 text-green-800 px-4 py-2 rounded-lg">
                            <i class="fas fa-check-circle mr-2"></i>Claimed
                        </span>
                    <% } %>
                </div>
            </div>
        </div>
    </section>

    <!-- Tabs -->
    <section class="py-8 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8">
                    <button class="tab-btn py-2 px-1 border-b-2 border-blue-500 text-blue-600" data-tab="about">
                        About
                    </button>
                    <button class="tab-btn py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700" data-tab="services">
                        Services
                    </button>
                    <button class="tab-btn py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700" data-tab="reviews">
                        Reviews
                    </button>
                    <button class="tab-btn py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700" data-tab="contact">
                        Contact
                    </button>
                </nav>
            </div>
        </div>
    </section>

    <!-- Tab Content -->
    <section class="py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- About Tab -->
            <div id="about-tab" class="tab-content">
                <h2 class="text-2xl font-bold mb-4">About <%= business.name %></h2>
                <p class="text-gray-600 mb-6"><%= business.description %></p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <h3 class="text-lg font-semibold mb-4">Business Information</h3>
                        <dl class="space-y-2">
                            <dt class="font-medium">Category:</dt>
                            <dd class="text-gray-600"><%= business.category_name %></dd>
                            
                            <dt class="font-medium">Phone:</dt>
                            <dd class="text-gray-600"><%= business.phone %></dd>
                            
                            <% if (business.email) { %>
                                <dt class="font-medium">Email:</dt>
                                <dd class="text-gray-600"><%= business.email %></dd>
                            <% } %>
                            
                            <% if (business.website) { %>
                                <dt class="font-medium">Website:</dt>
                                <dd class="text-gray-600">
                                    <a href="<%= business.website %>" target="_blank" class="text-blue-600 hover:underline">
                                        <%= business.website %>
                                    </a>
                                </dd>
                            <% } %>
                        </dl>
                    </div>
                    
                    <div>
                        <h3 class="text-lg font-semibold mb-4">Hours of Operation</h3>
                        <% if (business.business_hours && Object.keys(business.business_hours).length > 0) { %>
                            <dl class="space-y-2">
                                <% Object.entries(business.business_hours).forEach(([day, hours]) => { %>
                                    <dt class="font-medium"><%= day %>:</dt>
                                    <dd class="text-gray-600"><%= hours %></dd>
                                <% }) %>
                            </dl>
                        <% } else { %>
                            <p class="text-gray-600">Hours not specified</p>
                        <% } %>
                    </div>
                </div>
            </div>

            <!-- Services Tab -->
            <div id="services-tab" class="tab-content hidden">
                <h2 class="text-2xl font-bold mb-4">Services Offered</h2>
                <% if (business.services && business.services.length > 0) { %>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <% business.services.forEach(service => { %>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h4 class="font-semibold"><%= service.name %></h4>
                                <p class="text-gray-600 text-sm"><%= service.description %></p>
                            </div>
                        <% }) %>
                    </div>
                <% } else { %>
                    <p class="text-gray-600">Services information not available</p>
                <% } %>
            </div>

            <!-- Reviews Tab -->
            <div id="reviews-tab" class="tab-content hidden">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold">Customer Reviews</h2>
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition">
                        Write a Review
                    </button>
                </div>
                
                <% if (reviews.length > 0) { %>
                    <div class="space-y-6">
                        <% reviews.forEach(review => { %>
                            <div class="bg-gray-50 p-6 rounded-lg">
                                <div class="flex items-center mb-2">
                                    <div class="flex text-yellow-400">
                                        <% for (let i = 1; i <= 5; i++) { %>
                                            <i class="fas fa-star <%= i <= review.rating ? 'text-yellow-400' : 'text-gray-300' %>"></i>
                                        <% } %>
                                    </div>
                                    <span class="ml-2 font-semibold"><%= review.title %></span>
                                </div>
                                
                                <p class="text-gray-600 mb-2"><%= review.content %></p>
                                
                                <div class="text-sm text-gray-500">
                                    By <%= review.first_name %> <%= review.last_name %> • <%= new Date(review.created_at).toLocaleDateString() %>
                                </div>
                            </div>
                        <% }) %>
                    </div>
                <% } else { %>
                    <p class="text-gray-600">No reviews yet. Be the first to write one!</p>
                <% } %>
            </div>

            <!-- Contact Tab -->
            <div id="contact-tab" class="tab-content hidden">
                <h2 class="text-2xl font-bold mb-4">Contact Information</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <h3 class="text-lg font-semibold mb-4">Get in Touch</h3>
                        <div class="space-y-4">
                            <div>
                                <i class="fas fa-map-marker-alt mr-2"></i>
                                <%= business.address %><br>
                                <%= business.city_name %>, <%= business.state_code %> <%= business.postal_code %>
                            </div>
                            
                            <div>
                                <i class="fas fa-phone mr-2"></i>
                                <a href="tel:<%= business.phone %>" class="text-blue-600 hover:underline">
                                    <%= business.phone %>
                                </a>
                            </div>
                            
                            <% if (business.email) { %>
                                <div>
                                    <i class="fas fa-envelope mr-2"></i>
                                    <a href="mailto:<%= business.email %>" class="text-blue-600 hover:underline">
                                        <%= business.email %>
                                    </a>
                                </div>
                            <% } %>
                        </div>
                    </div>
                    
                    <div>
                        <h3 class="text-lg font-semibold mb-4">Business Hours</h3>
                        <% if (business.business_hours && Object.keys(business.business_hours).length > 0) { %>
                            <div class="space-y-2">
                                <% Object.entries(business.business_hours).forEach(([day, hours]) => { %>
                                    <div class="flex justify-between">
                                        <span class="font-medium"><%= day %>:</span>
                                        <span><%= hours %></span>
                                    </div>
                                <% }) %>
                            </div>
                        <% } else { %>
                            <p class="text-gray-600">Hours not specified</p>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Similar Businesses -->
    <% if (similarBusinesses.length > 0) { %>
        <section class="py-16 bg-gray-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <h2 class="text-2xl font-bold mb-8">Similar Businesses</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <% similarBusinesses.forEach(similar => { %>
                        <div class="bg-white p-6 rounded-lg shadow-md">
                            <h3 class="text-lg font-semibold mb-2">
                                <a href="/businesses/<%= similar.slug %>" class="text-gray-900 hover:text-blue-600">
                                    <%= similar.name %>
                                </a>
                            </h3>
                            <p class="text-gray-600 text-sm mb-2"><%= similar.description?.substring(0, 100) %>...</p>
                            <div class="flex items-center">
                                <div class="flex text-yellow-400">
                                    <% for (let i = 1; i <= 5; i++) { %>
                                        <i class="fas fa-star <%= i <= similar.rating ? 'text-yellow-400' : 'text-gray-300' %>"></i>
                                    <% } %>
                                </div>
                                <span class="ml-2 text-sm text-gray-600"><%= similar.rating %></span>
                            </div>
                        </div>
                    <% }) %>
                </div>
            </div>
        </section>
    <% } %>
`, script: `
    <script>
        // Tab functionality
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const tabName = btn.getAttribute('data-tab');
                
                // Remove active class from all buttons
                tabBtns.forEach(b => {
                    b.classList.remove('border-blue-500', 'text-blue-600');
                    b.classList.add('border-transparent', 'text-gray-500');
                });
                
                // Hide all tab contents
                tabContents.forEach(content => {
                    content.classList.add('hidden');
                });
                
                // Show selected tab
                btn.classList.remove('border-transparent', 'text-gray-500');
                btn.classList.add('border-blue-500', 'text-blue-600');
                document.getElementById(tabName + '-tab').classList.remove('hidden');
            });
        });
    </script>
`, title: business.name, script: \`
    <script>
        // Contact form submission
        document.getElementById('contact-form')?.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);

            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Sending...';
            submitBtn.disabled = true;

            // Simulate form submission (replace with actual API call)
            setTimeout(() => {
                alert('Message sent successfully!');
                this.reset();
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }, 1000);
        });
    </script>
\` }) %>
