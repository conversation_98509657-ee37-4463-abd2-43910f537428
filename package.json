{"name": "pressure-washing-directory", "version": "1.0.0", "description": "Comprehensive national pressure washing directory application", "main": "server.js", "scripts": {"dev": "nodemon server.js", "start": "node server.js", "build": "npm run build:css && npm run build:js", "build:css": "postcss public/css/tailwind.css -o public/css/styles.css", "build:js": "webpack --mode production", "db:migrate": "node scripts/migrate.js", "db:seed": "node scripts/seed.js"}, "dependencies": {"express": "^4.18.2", "pg": "^8.11.3", "multer": "^1.4.5-lts.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "dotenv": "^16.3.1", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "nodemailer": "^6.9.4", "compression": "^1.7.4", "express-validator": "^7.0.1", "slugify": "^1.6.6", "csv-parser": "^3.0.0", "ejs": "^3.1.9"}, "devDependencies": {"nodemon": "^3.0.1", "tailwindcss": "^3.3.3", "postcss": "^8.4.29", "autoprefixer": "^10.4.15", "webpack": "^5.88.2", "webpack-cli": "^5.1.4"}}