<%- include('layout', { body: `
    <!-- States Header -->
    <section class="bg-blue-600 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h1 class="text-4xl font-bold mb-4">Browse by State</h1>
            <p class="text-xl text-blue-100">Find pressure washing services in your state</p>
        </div>
    </section>

    <!-- States Grid -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <% states.forEach(state => { %>
                    <a href="/states/<%= state.slug %>" 
                       class="state-card bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                        <div class="text-center">
                            <h3 class="text-xl font-semibold text-gray-900 mb-2"><%= state.name %></h3>
                            <p class="text-gray-600 mb-2"><%= state.code %></p>
                            <p class="text-sm text-gray-500">
                                <%= state.business_count %> businesses
                            </p>
                        </div>
                    </a>
                <% }) %>
            </div>
        </div>
    </section>

    <!-- Popular Cities -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-center mb-8">Popular Cities</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
                <% const popularCities = [
                    { name: 'Phoenix', state: 'Arizona', slug: 'arizona/phoenix' },
                    { name: 'Los Angeles', state: 'California', slug: 'california/los-angeles' },
                    { name: 'Houston', state: 'Texas', slug: 'texas/houston' },
                    { name: 'Miami', state: 'Florida', slug: 'florida/miami' },
                    { name: 'Dallas', state: 'Texas', slug: 'texas/dallas' }
                ]; %>
                
                <% popularCities.forEach(city => { %>
                    <a href="/states/<%= city.slug %>" 
                       class="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition duration-300 text-center">
                        <h4 class="font-semibold text-gray-900"><%= city.name %></h4>
                        <p class="text-sm text-gray-600"><%= city.state %></p>
                    </a>
                <% }) %>
            </div>
        </div>
    </section>
`, title: 'Browse by State', script: '' }) %>
