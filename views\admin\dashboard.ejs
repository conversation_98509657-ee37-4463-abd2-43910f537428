<%- include('layout', { body: `
    <!-- <PERSON><PERSON> -->
    <section class="bg-gray-800 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h1 class="text-4xl font-bold mb-4">Admin Dashboard</h1>
            <p class="text-xl text-gray-300">Manage your pressure washing directory</p>
        </div>
    </section>

    <!-- Stats -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6">
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-center">
                        <div class="bg-blue-100 p-3 rounded-lg">
                            <i class="fas fa-building text-blue-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Businesses</p>
                            <p class="text-2xl font-bold text-gray-900"><%= stats.total_businesses %></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-center">
                        <div class="bg-green-100 p-3 rounded-lg">
                            <i class="fas fa-check-circle text-green-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Verified</p>
                            <p class="text-2xl font-bold text-gray-900"><%= stats.verified_businesses %></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-center">
                        <div class="bg-purple-100 p-3 rounded-lg">
                            <i class="fas fa-handshake text-purple-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Claimed</p>
                            <p class="text-2xl font-bold text-gray-900"><%= stats.claimed_businesses %></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-center">
                        <div class="bg-yellow-100 p-3 rounded-lg">
                            <i class="fas fa-star text-yellow-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Reviews</p>
                            <p class="text-2xl font-bold text-gray-900"><%= stats.total_reviews %></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-center">
                        <div class="bg-red-100 p-3 rounded-lg">
                            <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Pending Claims</p>
                            <p class="text-2xl font-bold text-gray-900"><%= stats.pending_claims %></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-center">
                        <div class="bg-indigo-100 p-3 rounded-lg">
                            <i class="fas fa-users text-indigo-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Users</p>
                            <p class="text-2xl font-bold text-gray-900"><%= stats.total_users %></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Import Section -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="bg-white rounded-lg shadow-md p-8">
                <h2 class="text-2xl font-bold mb-6">Import Businesses</h2>
                
                <div class="mb-6">
                    <p class="text-gray-600 mb-4">
                        Upload a CSV file with business information. The file should include columns for:
                        name, address, city, state, phone, email, website, description, category, postal_code, verified
                    </p>
                    
                    <form id="import-form" enctype="multipart/form-data">
                        <div class="mb-4">
                            <input 
                                type="file" 
                                name="file" 
                                accept=".csv" 
                                class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                                required
                            >
                        </div>
                        
                        <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition">
                            <i class="fas fa-upload mr-2"></i>Import CSV
                        </button>
                    </form>
                    
                    <div id="import-status" class="mt-4"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Recent Claims -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-2xl font-bold mb-6">Recent Business Claims</h2>
            
            <% if (claims.length > 0) { %>
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Business
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Claimant
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Status
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Date
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <% claims.forEach(claim => { %>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900"><%= claim.business_name %></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900"><%= claim.user_email %></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <%= claim.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : claim.status === 'approved' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                                            <%= claim.status %>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <%= new Date(claim.created_at).toLocaleDateString() %>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <% if (claim.status === 'pending') { %>
                                            <button onclick="processClaim('<%= claim.id %>', 'approve')" 
                                                    class="text-green-600 hover:text-green-900 mr-3">
                                                Approve
                                            </button>
                                            <button onclick="processClaim('<%= claim.id %>', 'reject')" 
                                                    class="text-red-600 hover:text-red-900">
                                                Reject
                                            </button>
                                        <% } %>
                                    </td>
                                </tr>
                            <% }) %>
                        </tbody>
                    </table>
                </div>
            <% } else { %>
                <p class="text-gray-600">No recent claims</p>
            <% } %>
        </div>
    </section>

    <!-- Import History -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-2xl font-bold mb-6">Import History</h2>
            
            <% if (imports.length > 0) { %>
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Filename
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Total Records
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Successful
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Failed
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Date
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Imported By
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <% imports.forEach(import => { %>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        <%= import.filename %>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <%= import.total_records %>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <%= import.successful_imports %>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <%= import.failed_imports %>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <%= new Date(import.created_at).toLocaleDateString() %>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <%= import.user_email || 'System' %>
                                    </td>
                                </tr>
                            <% }) %>
                        </tbody>
                    </table>
                </div>
            <% } else { %>
                <p class="text-gray-600">No import history</p>
            <% } %>
        </div>
    </section>
`, script: `
    <script>
        // Import CSV
        document.getElementById('import-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData();
            formData.append('file', document.querySelector('input[name="file"]').files[0]);

            try {
                const response = await fetch('/admin/import', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'Authorization': 'Bearer ' + localStorage.getItem('token')
                    }
                });

                const result = await response.json();
                
                if (response.ok) {
                    document.getElementById('import-status').innerHTML = 
                        '<div class="bg-green-100 text-green-800 p-4 rounded-lg">' +
                        'Import completed successfully!<br>' +
                        'Total: ' + result.total + '<br>' +
                        'Successful: ' + result.successful + '<br>' +
                        'Failed: ' + result.failed +
                        '</div>';
                    document.getElementById('import-form').reset();
                    
                    // Reload page after 2 seconds
                    setTimeout(() => location.reload(), 2000);
                } else {
                    document.getElementById('import-status').innerHTML = 
                        '<div class="bg-red-100 text-red-800 p-4 rounded-lg">Error: ' + result.error + '</div>';
                }
            } catch (error) {
                document.getElementById('import-status').innerHTML = 
                    '<div class="bg-red-100 text-red-800 p-4 rounded-lg">Import failed. Please try again.</div>';
            }
        });

        // Process claims
        async function processClaim(claimId, action) {
            if (confirm('Are you sure you want to ' + action + ' this claim?')) {
                try {
                    const response = await fetch('/admin/claims/' + claimId + '/process', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': 'Bearer ' + localStorage.getItem('token')
                        },
                        body: JSON.stringify({ action })
                    });

                    const result = await response.json();
                    
                    if (response.ok) {
                        alert(result.message);
                        location.reload();
                    } else {
                        alert('Error: ' + result.error);
                    }
                } catch (error) {
                    alert('Error processing claim');
                }
            }
        }
    </script>
`, title: 'Admin Dashboard', script: '' }) %>
