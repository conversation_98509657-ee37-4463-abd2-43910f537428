<%- include('layout', { body: `
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-blue-600 to-cyan-500 text-white">
        <div class="absolute inset-0 water-animation opacity-20"></div>
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold mb-6">
                    Find Trusted Pressure Washing Services
                </h1>
                <p class="text-xl md:text-2xl mb-8 text-blue-100">
                    Connect with verified professionals across the United States
                </p>
                
                <!-- Search Bar -->
                <div class="max-w-2xl mx-auto">
                    <form action="/search" method="GET" class="relative">
                        <input 
                            type="text" 
                            name="q" 
                            placeholder="Search for pressure washing services..." 
                            class="w-full px-6 py-4 text-lg rounded-full text-gray-900 focus:outline-none focus:ring-4 focus:ring-blue-300"
                        >
                        <button 
                            type="submit" 
                            class="absolute right-2 top-2 bg-blue-600 text-white px-6 py-2 rounded-full hover:bg-blue-700 transition"
                        >
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Popular States -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-center mb-12">Browse by State</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                <% popularStates.forEach(state => { %>
                    <a href="/states/<%= state.slug %>" 
                       class="block p-6 bg-gray-50 rounded-lg hover:bg-blue-50 transition text-center">
                        <div class="text-2xl font-bold text-blue-600"><%= state.code %></div>
                        <div class="text-sm text-gray-600"><%= state.name %></div>
                        <div class="text-xs text-gray-500"><%= state.business_count %> businesses</div>
                    </a>
                <% }) %>
            </div>
        </div>
    </section>

    <!-- Featured Businesses -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-center mb-12">Featured Businesses</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <% featuredBusinesses.forEach(business => { %>
                    <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-xl font-semibold">
                                    <a href="/businesses/<%= business.slug %>" class="text-gray-900 hover:text-blue-600">
                                        <%= business.name %>
                                    </a>
                                </h3>
                                <% if (business.is_verified) { %>
                                    <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                        <i class="fas fa-check-circle mr-1"></i>Verified
                                    </span>
                                <% } %>
                            </div>
                            
                            <p class="text-gray-600 mb-4"><%= business.description?.substring(0, 100) %>...</p>
                            
                            <div class="flex items-center mb-4">
                                <div class="flex text-yellow-400">
                                    <% for (let i = 1; i <= 5; i++) { %>
                                        <i class="fas fa-star <%= i <= business.rating ? 'text-yellow-400' : 'text-gray-300' %>"></i>
                                    <% } %>
                                </div>
                                <span class="ml-2 text-gray-600"><%= business.rating %> (<%= business.review_count %> reviews)</span>
                            </div>
                            
                            <div class="text-sm text-gray-600">
                                <i class="fas fa-map-marker-alt mr-1"></i>
                                <%= business.city_name %>, <%= business.state_code %>
                            </div>
                            
                            <div class="text-sm text-gray-600 mt-2">
                                <i class="fas fa-phone mr-1"></i>
                                <%= business.phone %>
                            </div>
                        </div>
                    </div>
                <% }) %>
            </div>
            
            <div class="text-center mt-12">
                <a href="/search" class="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition">
                    View All Businesses
                </a>
            </div>
        </div>
    </section>

    <!-- Categories -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-center mb-12">Service Categories</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <% categories.forEach(category => { %>
                    <a href="/search?category=<%= category.slug %>" 
                       class="block p-6 bg-gray-50 rounded-lg hover:bg-blue-50 transition">
                        <h3 class="text-xl font-semibold mb-2"><%= category.name %></h3>
                        <p class="text-gray-600 mb-4"><%= category.description %></p>
                        <div class="text-blue-600 font-medium">
                            <%= category.business_count %> businesses
                        </div>
                    </a>
                <% }) %>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-16 bg-blue-600 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl font-bold mb-4">Ready to Get Started?</h2>
            <p class="text-xl mb-8 text-blue-100">Find the perfect pressure washing service for your needs</p>
            <a href="/search" class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition">
                Search Now
            </a>
        </div>
    </section>
`, title: 'National Pressure Washing Directory', script: '' }) %>
