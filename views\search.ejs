<%- include('layout', { body: `
    <!-- Search Header -->
    <section class="bg-blue-600 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h1 class="text-4xl font-bold mb-4">Search Pressure Washing Services</h1>
            <p class="text-xl text-blue-100">Find the perfect service provider for your needs</p>
        </div>
    </section>

    <!-- Search Form -->
    <section class="py-8 bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <form action="/search" method="GET" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <input 
                    type="text" 
                    name="q" 
                    value="<%= query.q || '' %>" 
                    placeholder="Search businesses..." 
                    class="col-span-1 md:col-span-2 px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                
                <select name="state" class="px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All States</option>
                    <% states.forEach(state => { %>
                        <option value="<%= state.slug %>" <%= query.state === state.slug ? 'selected' : '' %>>
                            <%= state.name %>
                        </option>
                    <% }) %>
                </select>
                
                <select name="city" class="px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Cities</option>
                </select>
                
                <select name="category" class="px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Categories</option>
                    <% categories.forEach(category => { %>
                        <option value="<%= category.slug %>" <%= query.category === category.slug ? 'selected' : '' %>>
                            <%= category.name %>
                        </option>
                    <% }) %>
                </select>
                
                <select name="sort" class="px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="rating" <%= query.sort === 'rating' ? 'selected' : '' %>>Highest Rated</option>
                    <option value="name" <%= query.sort === 'name' ? 'selected' : '' %>>Name A-Z</option>
                </select>
                
                <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition">
                    <i class="fas fa-search mr-2"></i>Search
                </button>
            </form>
        </div>
    </section>

    <!-- Results -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center mb-8">
                <h2 class="text-2xl font-bold">
                    <%= businesses.length %> Results Found
                </h2>
            </div>
            
            <% if (businesses.length === 0) { %>
                <div class="text-center py-12">
                    <i class="fas fa-search text-6xl text-gray-300 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">No businesses found</h3>
                    <p class="text-gray-500">Try adjusting your search criteria</p>
                </div>
            <% } else { %>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <% businesses.forEach(business => { %>
                        <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition">
                            <div class="p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <h3 class="text-xl font-semibold">
                                        <a href="/businesses/<%= business.slug %>" class="text-gray-900 hover:text-blue-600">
                                            <%= business.name %>
                                        </a>
                                    </h3>
                                    <% if (business.is_verified) { %>
                                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                            <i class="fas fa-check-circle mr-1"></i>Verified
                                        </span>
                                    <% } %>
                                </div>
                                
                                <p class="text-gray-600 mb-4"><%= business.description?.substring(0, 150) %>...</p>
                                
                                <div class="flex items-center mb-4">
                                    <div class="flex text-yellow-400">
                                        <% for (let i = 1; i <= 5; i++) { %>
                                            <i class="fas fa-star <%= i <= business.rating ? 'text-yellow-400' : 'text-gray-300' %>"></i>
                                        <% } %>
                                    </div>
                                    <span class="ml-2 text-gray-600"><%= business.rating %> (<%= business.review_count %> reviews)</span>
                                </div>
                                
                                <div class="text-sm text-gray-600">
                                    <i class="fas fa-map-marker-alt mr-1"></i>
                                    <%= business.city_name %>, <%= business.state_code %>
                                </div>
                                
                                <div class="text-sm text-gray-600 mt-2">
                                    <i class="fas fa-phone mr-1"></i>
                                    <%= business.phone %>
                                </div>
                                
                                <div class="text-sm text-gray-600 mt-2">
                                    <i class="fas fa-tag mr-1"></i>
                                    <%= business.category_name %>
                                </div>
                            </div>
                        </div>
                    <% }) %>
                </div>
            <% } %>
        </div>
    </section>
`, title: 'Search Pressure Washing Services', script: '' }) %>
